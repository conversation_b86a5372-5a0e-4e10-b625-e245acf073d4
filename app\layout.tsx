import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { AuthProvider } from "@/components/auth/auth-context";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "C3ALabs - AI Memory Agent",
  description: "Experience the future of AI conversations with our advanced memory agent. Personalized, contextual, and intelligent interactions that remember and learn from every conversation.",
  keywords: "AI, memory agent, artificial intelligence, chatbot, personalized AI, conversation AI, C3ALabs",
  authors: [{ name: "C3<PERSON><PERSON><PERSON>" }],
  creator: "C3ALabs",
  openGraph: {
    title: "C3ALabs - AI Memory Agent",
    description: "Experience the future of AI conversations with our advanced memory agent.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "C3ALabs - AI Memory Agent",
    description: "Experience the future of AI conversations with our advanced memory agent.",
  },
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}