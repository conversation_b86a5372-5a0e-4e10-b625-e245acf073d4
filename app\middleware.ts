import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Get the pathname of the request (e.g. /, /login, /chat)
  const path = request.nextUrl.pathname

  // Check if this is a login page request
  const isLoginPage = path === '/login'
  
  // Get the token from the cookies (we'll use this to check authentication on server side)
  const token = request.cookies.get('c3alabs_authenticated')?.value

  // If user is on login page and has a token, redirect to home
  if (isLoginPage && token === 'true') {
    return NextResponse.redirect(new URL('/', request.url))
  }

  // If user is not on login page and doesn't have a token, redirect to login
  if (!isLoginPage && token !== 'true') {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}