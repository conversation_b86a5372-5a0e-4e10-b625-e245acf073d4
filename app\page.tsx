"use client";

import { useAuth } from "@/components/auth/auth-context";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { LoginPage } from "@/components/auth/login-page";

export default function Home() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !loading && isAuthenticated) {
      router.push("/landing");
    }
  }, [mounted, loading, isAuthenticated, router]);

  // Show loading while checking auth or during redirect
  if (!mounted || loading || isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-8 h-8 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
          <div className="text-white text-lg">
            {isAuthenticated ? "Redirecting..." : "Loading..."}
          </div>
        </div>
      </div>
    );
  }

  return <LoginPage />;
}