"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";

interface AuthContextType {
  isAuthenticated: boolean;
  user: string | null;
  login: (username: string) => void;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = () => {
    try {
      if (typeof window !== "undefined") {
        const authenticated = localStorage.getItem("c3alabs_authenticated");
        const savedUser = localStorage.getItem("c3alabs_user");
        
        if (authenticated === "true" && savedUser) {
          setIsAuthenticated(true);
          setUser(savedUser);
        } else {
          setIsAuthenticated(false);
          setUser(null);
        }
      }
    } catch (error) {
      console.error("Auth check failed:", error);
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const login = (username: string) => {
    if (typeof window !== "undefined") {
      // Set localStorage first
      localStorage.setItem("c3alabs_authenticated", "true");
      localStorage.setItem("c3alabs_user", username);
      
      // Set cookie for middleware
      document.cookie = "c3alabs_authenticated=true; path=/; max-age=86400"; // 24 hours
      
      // Update state
      setIsAuthenticated(true);
      setUser(username);
      
      // Force a page reload to trigger middleware and redirect properly
      window.location.href = "/landing";
    }
  };

  const logout = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("c3alabs_authenticated");
      localStorage.removeItem("c3alabs_user");
      
      // Remove cookie
      document.cookie = "c3alabs_authenticated=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      
      setIsAuthenticated(false);
      setUser(null);
      
      // Force redirect to login
      window.location.href = "/login";
    }
  };

  // Redirect logic - Only handle client-side redirects when middleware doesn't handle them
  useEffect(() => {
    if (!loading && typeof window !== "undefined") {
      // Let middleware handle most redirects, but handle specific cases
      if (isAuthenticated && pathname === "/") {
        router.replace("/landing");
      }
    }
  }, [isAuthenticated, loading, pathname, router]);

  const value = {
    isAuthenticated,
    user,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}