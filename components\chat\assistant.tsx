"use client";

import { Assistant<PERSON>untimeProvider } from "@assistant-ui/react";
import { useChatRuntime } from "@assistant-ui/react-ai-sdk";
import { EnhancedThread } from "@/components/chat/enhanced-thread";
import { EnhancedThreadList } from "@/components/chat/enhanced-thread-list";
import { useAuth } from "@/components/auth/auth-context";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { AlignJustify, ArrowLeft, LogOut } from "lucide-react";
import { Button } from "@/components/ui-enhanced/button";
import Link from "next/link";

const useUserId = () => {
  const [userId, setUserId] = useState<string>("");

  useEffect(() => {
    let id = localStorage.getItem("userId");
    if (!id) {
      id = uuidv4();
      localStorage.setItem("userId", id);
    }
    setUserId(id);
  }, []);

  const resetUserId = () => {
    const newId = uuidv4();
    localStorage.setItem("userId", newId);
    setUserId(newId);
    // Clear all threads from localStorage
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('thread:')) {
        localStorage.removeItem(key);
      }
    });
    // Force reload to clear all states
    window.location.reload();
  };

  return { userId, resetUserId };
};

export const Assistant = () => {
  const { userId, resetUserId } = useUserId();
  const { user, logout } = useAuth();
  const runtime = useChatRuntime({
    api: "/api/chat",
    body: { userId },
  });

  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Set dark mode by default
  useEffect(() => {
    document.documentElement.classList.add("dark");
  }, []);

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 text-white min-h-screen">
        {/* Animated background */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -inset-10 opacity-10">
            <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
          </div>
        </div>

        <header className="h-16 border-b border-white/10 flex items-center justify-between px-4 sm:px-6 bg-white/5 backdrop-blur-xl relative z-10">
          <div className="flex items-center space-x-4">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <Button variant="ghost" size="sm" className="p-2 text-white hover:text-blue-400">
                <ArrowLeft className="w-4 h-4" />
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="text-xl font-bold text-white">C3ALabs</span>
            </div>
          </div>

          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setSidebarOpen(true)}
            className="text-white md:hidden hover:text-blue-400"
          >
            <AlignJustify size={24} />
          </Button>

          {/* User info and logout */}
          <div className="hidden md:flex items-center space-x-4">
            <span className="text-sm text-gray-300">Welcome, {user}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={logout}
              className="text-white hover:text-red-400 transition-colors"
              title="Logout"
            >
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-[280px_1fr] gap-x-0 h-[calc(100vh-4rem)]">
          <EnhancedThreadList onResetUserId={resetUserId} />
          <EnhancedThread 
            sidebarOpen={sidebarOpen} 
            setSidebarOpen={setSidebarOpen} 
            onResetUserId={resetUserId} 
            user={user}
            onLogout={logout}
          />
        </div>

        <style jsx>{`
          @keyframes blob {
            0% {
              transform: translate(0px, 0px) scale(1);
            }
            33% {
              transform: translate(30px, -50px) scale(1.1);
            }
            66% {
              transform: translate(-20px, 20px) scale(0.9);
            }
            100% {
              transform: translate(0px, 0px) scale(1);
            }
          }
          .animate-blob {
            animation: blob 7s infinite;
          }
          .animation-delay-2000 {
            animation-delay: 2s;
          }
          .animation-delay-4000 {
            animation-delay: 4s;
          }
        `}</style>
      </div>
    </AssistantRuntimeProvider>
  );
};