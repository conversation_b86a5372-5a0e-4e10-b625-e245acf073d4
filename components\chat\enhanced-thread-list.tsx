import type { FC } from "react";
import {
  ThreadListItemPrimitive,
  ThreadListPrimitive,
} from "@assistant-ui/react";
import { ArchiveIcon, PlusIcon, RefreshCwIcon } from "lucide-react";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui-enhanced/button";
import { TooltipIconButton } from "@/components/assistant-ui/tooltip-icon-button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface EnhancedThreadListProps {
  onResetUserId?: () => void;
}

export const EnhancedThreadList: FC<EnhancedThreadListProps> = ({ onResetUserId }) => {
  const [open, setOpen] = useState(false);
  
  return (
    <div className="flex-col h-full border-r border-white/10 glass-sidebar p-4 overflow-y-auto hidden md:flex">
      <ThreadListPrimitive.Root className="flex flex-col justify-between h-full items-stretch gap-4">
        <div className="flex flex-col h-full items-stretch gap-4">
          <ThreadListNew />
          
          {/* Quick Actions - Removed */}

          <div className="flex justify-between items-center px-2">
            <h2 className="text-sm font-medium text-gray-300">
              Recent Chats
            </h2>
            {onResetUserId && (
              <AlertDialog open={open} onOpenChange={setOpen}>
                <AlertDialogTrigger asChild>
                  <TooltipIconButton
                    tooltip="Reset Memory"
                    className="hover:text-purple-400 text-white size-4 p-0"
                    variant="ghost"
                  >
                    <RefreshCwIcon className="w-4 h-4" />
                  </TooltipIconButton>
                </AlertDialogTrigger>
                <AlertDialogContent className="glass-card border-white/20">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="text-white">
                      Reset Memory
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-gray-300">
                      This will permanently delete all your chat history and
                      memories. This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel className="glass-button">
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => {
                        onResetUserId();
                        setOpen(false);
                      }}
                      className="glass-button-primary"
                    >
                      Reset
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
          <ThreadListItems />
        </div>
      </ThreadListPrimitive.Root>

      <style jsx>{`
        .glass-sidebar {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
        }
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .glass-button {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-button-primary {
          background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .enhanced-sidebar-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .enhanced-sidebar-button {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .enhanced-sidebar-button:hover {
          background: rgba(255, 255, 255, 0.2);
        }
        .enhanced-new-thread-button {
          background: linear-gradient(45deg, rgba(139, 92, 246, 0.9), rgba(124, 58, 237, 0.9));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .enhanced-new-thread-button:hover {
          background: linear-gradient(45deg, rgba(139, 92, 246, 1), rgba(124, 58, 237, 1));
          box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }
      `}</style>
    </div>
  );
};

const ThreadListNew: FC = () => {
  return (
    <ThreadListPrimitive.New asChild>
      <Button
        className="chat-sidebar-new-button hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2 rounded-xl px-4 py-3 text-white font-semibold"
        variant="glass-primary"
      >
        <PlusIcon className="w-4 h-4" />
        New Conversation
      </Button>
    </ThreadListPrimitive.New>
  );
};

const ThreadListItems: FC = () => {
  return <ThreadListPrimitive.Items components={{ ThreadListItem }} />;
};

const ThreadListItem: FC = () => {
  return (
    <ThreadListItemPrimitive.Root className="glass-thread-item hover:scale-[1.02] transition-all duration-200 focus-visible:ring-purple-500 flex items-center gap-2 rounded-lg focus-visible:outline-none focus-visible:ring-2">
      <ThreadListItemPrimitive.Trigger className="flex-grow px-3 py-3 text-start">
        <ThreadListItemTitle />
      </ThreadListItemPrimitive.Trigger>
      <ThreadListItemArchive />
    </ThreadListItemPrimitive.Root>
  );
};

const ThreadListItemTitle: FC = () => {
  return (
    <p className="text-sm text-white hover:text-purple-300 transition-colors">
      <ThreadListItemPrimitive.Title fallback="New Chat" />
    </p>
  );
};

const ThreadListItemArchive: FC = () => {
  return (
    <ThreadListItemPrimitive.Archive asChild>
      <TooltipIconButton
        className="hover:text-purple-400 text-white/70 ml-auto mr-3 size-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
        variant="ghost"
        tooltip="Archive thread"
      >
        <ArchiveIcon />
      </TooltipIconButton>
    </ThreadListItemPrimitive.Archive>
  );
};