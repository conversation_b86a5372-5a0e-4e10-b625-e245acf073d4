"use client";

import {
  ActionBarPrimitive,
  BranchPickerPrimitive,
  ComposerPrimitive,
  MessagePrimitive,
  ThreadPrimitive,
  ThreadListItemPrimitive,
  ThreadListPrimitive,
  useMessage,
} from "@assistant-ui/react";
import type { FC } from "react";
import {
  ArrowDownIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CopyIcon,
  PencilIcon,
  RefreshCwIcon,
  SendHorizontalIcon,
  ArchiveIcon,
  PlusIcon,
  Sparkles,
  MessageSquare,
  LogOut,
  User,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Dispatch, SetStateAction, useState, useRef } from "react";
import { Button } from "@/components/ui-enhanced/button";
import { Card, CardContent } from "@/components/ui-enhanced/card";
import { ScrollArea } from "../ui/scroll-area";
import { TooltipIconButton } from "@/components/assistant-ui/tooltip-icon-button";
import { MemoryUI } from "@/components/assistant-ui/memory-ui";
import MarkdownRenderer from "../mem0/markdown";
import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface EnhancedThreadProps {
  sidebarOpen: boolean;
  setSidebarOpen: Dispatch<SetStateAction<boolean>>;
  onResetUserId?: () => void;
  user: string | null;
  onLogout: () => void;
}

export const EnhancedThread: FC<EnhancedThreadProps> = ({
  sidebarOpen,
  setSidebarOpen,
  onResetUserId,
  user,
  onLogout
}) => {
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const composerInputRef = useRef<HTMLTextAreaElement>(null);

  return (
    <ThreadPrimitive.Root
      className="bg-transparent box-border flex flex-col overflow-hidden relative h-full"
      style={{
        ["--thread-max-width" as string]: "48rem",
      }}
    >
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/40 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}

      {/* Mobile sidebar drawer */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-40 w-[75%] glass-card shadow-lg rounded-r-lg transform transition-transform duration-300 ease-in-out md:hidden",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between border-b border-white/10 p-4">
            <h2 className="font-medium text-white">Settings</h2>
            <div className="flex items-center gap-2">
              {onResetUserId && (
                <AlertDialog
                  open={resetDialogOpen}
                  onOpenChange={setResetDialogOpen}
                >
                  <AlertDialogTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <RefreshCwIcon className="w-4 h-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="glass-card border-white/20">
                    <AlertDialogHeader>
                      <AlertDialogTitle className="text-white">
                        Reset Memory
                      </AlertDialogTitle>
                      <AlertDialogDescription className="text-gray-300">
                        This will permanently delete all your chat history and
                        memories. This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel className="mobile-button">
                        Cancel
                      </AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => {
                          onResetUserId();
                          setResetDialogOpen(false);
                        }}
                        className="mobile-button-primary"
                      >
                        Reset
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="text-white h-8 w-8 p-0"
              >
                ✕
              </Button>
            </div>
          </div>
          <div className="flex-1 overflow-y-auto p-3">
            <div className="flex flex-col justify-between items-stretch gap-3 h-full">
              <ThreadListPrimitive.Root className="flex flex-col items-stretch gap-3 h-full">
                <ThreadListPrimitive.New asChild>
                  <div className="flex items-center flex-col gap-3 w-full">
                    <Button className="chat-new-thread-button w-full" variant="glass-primary">
                      <PlusIcon className="w-4 h-4" />
                      New Thread
                    </Button>
                    
                    {/* User info and logout for mobile */}
                    <div className="w-full space-y-3 pt-4 border-t border-white/10">
                      <div className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/5">
                        <User className="w-4 h-4 text-blue-400" />
                        <span className="text-sm text-white">Welcome, {user}</span>
                      </div>
                      <Button
                        onClick={onLogout}
                        className="mobile-logout-button w-full"
                        variant="ghost"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                      </Button>
                    </div>
                  </div>
                </ThreadListPrimitive.New>
                <div className="mt-4 mb-2">
                  <h2 className="text-sm font-medium text-gray-300 px-2.5">
                    Recent Chats
                  </h2>
                </div>
                <ThreadListPrimitive.Items components={{ ThreadListItem }} />
              </ThreadListPrimitive.Root>
            </div>
          </div>
        </div>
      </div>

      <ScrollArea className="flex-1 w-full">
        <div className="flex h-full flex-col w-full items-center px-4 pt-8 justify-end">
          <ThreadWelcome
            composerInputRef={
              composerInputRef as React.RefObject<HTMLTextAreaElement>
            }
          />

          <ThreadPrimitive.Messages
            components={{
              UserMessage: UserMessage,
              EditComposer: EditComposer,
              AssistantMessage: AssistantMessage,
            }}
          />

          <ThreadPrimitive.If empty={false}>
            <div className="min-h-8 flex-grow" />
          </ThreadPrimitive.If>
        </div>
      </ScrollArea>

      <div className="sticky bottom-0 flex w-full max-w-[var(--thread-max-width)] flex-col items-center justify-end rounded-t-lg bg-transparent px-4 pb-4 mx-auto">
        <ThreadScrollToBottom />
        <Composer
          composerInputRef={
            composerInputRef as React.RefObject<HTMLTextAreaElement>
          }
        />
      </div>

      <style jsx>{`
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .chat-new-thread-button {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.8), rgba(34, 211, 238, 0.8));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .chat-new-thread-button:hover {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.9), rgba(34, 211, 238, 0.9));
          box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .chat-badge {
          background: rgba(59, 130, 246, 0.2);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .chat-cta-button {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.9), rgba(34, 211, 238, 0.9));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 12px;
        }
        .chat-cta-button:hover {
          background: linear-gradient(45deg, rgba(59, 130, 246, 1), rgba(34, 211, 238, 1));
          box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .mobile-button {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .mobile-button:hover {
          background: rgba(255, 255, 255, 0.2);
        }
        .mobile-button-primary {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.9), rgba(34, 211, 238, 0.9));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .mobile-button-primary:hover {
          background: linear-gradient(45deg, rgba(59, 130, 246, 1), rgba(34, 211, 238, 1));
        }
        .mobile-logout-button {
          color: white;
          border: 1px solid rgba(239, 68, 68, 0.3);
          background: rgba(239, 68, 68, 0.1);
        }
        .mobile-logout-button:hover {
          background: rgba(239, 68, 68, 0.2);
          border-color: rgba(239, 68, 68, 0.5);
        }
      `}</style>
    </ThreadPrimitive.Root>
  );
};

const ThreadScrollToBottom: FC = () => {
  return (
    <ThreadPrimitive.ScrollToBottom asChild>
      <TooltipIconButton
        tooltip="Scroll to bottom"
        variant="outline"
        className="absolute -top-8 rounded-full disabled:invisible glass-button"
      >
        <ArrowDownIcon className="text-white" />
      </TooltipIconButton>
    </ThreadPrimitive.ScrollToBottom>
  );
};

interface ThreadWelcomeProps {
  composerInputRef: React.RefObject<HTMLTextAreaElement>;
}

const ThreadWelcome: FC<ThreadWelcomeProps> = ({ composerInputRef }) => {
  return (
    <ThreadPrimitive.Empty>
      <div className="flex w-full flex-grow flex-col mt-8 md:h-[calc(100vh-15rem)]">
        <div className="flex w-full flex-grow flex-col items-center justify-start">
          <div className="flex flex-col items-center justify-center h-full">
            <Card className="glass-card mb-8 p-8 max-w-2xl mx-auto">
              <CardContent className="p-0 text-center">
                <div className="inline-flex items-center px-4 py-2 rounded-full chat-badge mb-6 hover:scale-105 transition-all duration-200">
                  <Sparkles className="w-4 h-4 text-blue-400 mr-2" />
                  <span className="text-sm text-blue-300 font-medium">C3ALabs Memory Agent</span>
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-4 text-center">
                  Start a conversation that <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">remembers</span>
                </div>
                <p className="text-center text-lg text-gray-300 mb-6">
                  Experience AI conversations that build context over time. Share your thoughts, 
                  preferences, and goals - I&apos;ll remember them for future interactions.
                </p>
                <Button 
                  onClick={() => composerInputRef.current?.focus()}
                  className="chat-cta-button px-6 py-3 text-lg font-semibold hover:scale-105 transition-all duration-200"
                >
                  <MessageSquare className="w-5 h-5 mr-2" />
                  Start Conversation
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center mt-8">
          <p className="mt-4 font-medium text-white mb-4">
            Try these conversation starters:
          </p>
          <ThreadWelcomeSuggestions composerInputRef={composerInputRef} />
        </div>
      </div>

      <style jsx>{`
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .chat-badge {
          background: rgba(59, 130, 246, 0.2);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(59, 130, 246, 0.3);
        }
        .chat-cta-button {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.9), rgba(34, 211, 238, 0.9));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 12px;
        }
        .chat-cta-button:hover {
          background: linear-gradient(45deg, rgba(59, 130, 246, 1), rgba(34, 211, 238, 1));
          box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
      `}</style>
    </ThreadPrimitive.Empty>
  );
};

interface ThreadWelcomeSuggestionsProps {
  composerInputRef: React.RefObject<HTMLTextAreaElement>;
}

const ThreadWelcomeSuggestions: FC<ThreadWelcomeSuggestionsProps> = ({ composerInputRef }) => {
  return (
    <div className="flex flex-col md:flex-row w-full md:items-stretch justify-center gap-4 items-center max-w-4xl">
      <ThreadPrimitive.Suggestion
        className="glass-suggestion flex max-w-sm grow basis-0 flex-col items-center justify-center rounded-2xl p-4 transition-all duration-200 hover:scale-105 cursor-pointer"
        prompt="I like to travel to "
        method="replace"
        onClick={() => {
          composerInputRef.current?.focus();
        }}
      >
        <span className="line-clamp-2 text-ellipsis text-sm font-semibold text-white">
          Travel Preferences
        </span>
        <span className="text-xs text-gray-300 mt-1">Tell me about places you love</span>
      </ThreadPrimitive.Suggestion>
      <ThreadPrimitive.Suggestion
        className="glass-suggestion flex max-w-sm grow basis-0 flex-col items-center justify-center rounded-2xl p-4 transition-all duration-200 hover:scale-105 cursor-pointer"
        prompt="I like to eat "
        method="replace"
        onClick={() => {
          composerInputRef.current?.focus();
        }}
      >
        <span className="line-clamp-2 text-ellipsis text-sm font-semibold text-white">
          Food & Dining
        </span>
        <span className="text-xs text-gray-300 mt-1">Share your culinary preferences</span>
      </ThreadPrimitive.Suggestion>
      <ThreadPrimitive.Suggestion
        className="glass-suggestion flex max-w-sm grow basis-0 flex-col items-center justify-center rounded-2xl p-4 transition-all duration-200 hover:scale-105 cursor-pointer"
        prompt="I am working on "
        method="replace"
        onClick={() => {
          composerInputRef.current?.focus();
        }}
      >
        <span className="line-clamp-2 text-ellipsis text-sm font-semibold text-white">
          Current Projects
        </span>
        <span className="text-xs text-gray-300 mt-1">Discuss your work and goals</span>
      </ThreadPrimitive.Suggestion>
    </div>
  );
};

interface ComposerProps {
  composerInputRef: React.RefObject<HTMLTextAreaElement>;
}

const Composer: FC<ComposerProps> = ({ composerInputRef }) => {
  return (
    <Card className="glass-card w-full">
      <CardContent className="p-0">
        <ComposerPrimitive.Root className="flex w-full items-end rounded-2xl px-4 py-2">
          <ComposerPrimitive.Input
            rows={1}
            autoFocus
            placeholder="Message C3ALabs..."
            className="placeholder:text-gray-400 max-h-40 flex-grow resize-none border-none bg-transparent px-2 py-3 text-sm outline-none focus:ring-0 disabled:cursor-not-allowed text-white"
            ref={composerInputRef}
          />
          <ComposerAction />
        </ComposerPrimitive.Root>
      </CardContent>
    </Card>
  );
};

const ComposerAction: FC = () => {
  return (
    <>
      <ThreadPrimitive.If running={false}>
        <ComposerPrimitive.Send asChild>
          <Button
            variant="glass-primary"
            size="sm"
            className="my-1 size-8 p-2 rounded-full chat-send-button"
          >
            <SendHorizontalIcon />
          </Button>
        </ComposerPrimitive.Send>
      </ThreadPrimitive.If>
      <ThreadPrimitive.If running>
        <ComposerPrimitive.Cancel asChild>
          <Button
            variant="glass-primary"
            size="sm"
            className="my-1 size-8 p-2 rounded-full chat-send-button"
          >
            <CircleStopIcon />
          </Button>
        </ComposerPrimitive.Cancel>
      </ThreadPrimitive.If>
    </>
  );
};

const UserMessage: FC = () => {
  return (
    <MessagePrimitive.Root className="grid auto-rows-auto grid-cols-[minmax(72px,1fr)_auto] gap-y-2 [&:where(>*)]:col-start-2 w-full max-w-[var(--thread-max-width)] py-4">
      <UserActionBar />

      <Card className="glass-user-message max-w-[calc(var(--thread-max-width)*0.8)] break-words col-start-2 row-start-2">
        <CardContent className="p-4">
          <MessagePrimitive.Content />
        </CardContent>
      </Card>

      <BranchPicker className="col-span-full col-start-1 row-start-3 -mr-1 justify-end" />
    </MessagePrimitive.Root>
  );
};

const UserActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohide="not-last"
      className="flex flex-col items-end col-start-1 row-start-2 mr-3 mt-2.5"
    >
      <ActionBarPrimitive.Edit asChild>
        <TooltipIconButton
          tooltip="Edit"
          className="text-white hover:text-blue-300 glass-button"
        >
          <PencilIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Edit>
    </ActionBarPrimitive.Root>
  );
};

const EditComposer: FC = () => {
  return (
    <Card className="glass-card my-4 w-full max-w-[var(--thread-max-width)]">
      <CardContent className="p-4">
        <ComposerPrimitive.Root className="flex w-full flex-col gap-2">
          <ComposerPrimitive.Input className="text-white flex h-8 w-full resize-none bg-transparent outline-none" />
          <div className="flex items-center justify-center gap-2 self-end">
            <ComposerPrimitive.Cancel asChild>
              <Button variant="ghost" className="text-white">
                Cancel
              </Button>
            </ComposerPrimitive.Cancel>
            <ComposerPrimitive.Send asChild>
              <Button className="chat-send-button">
                Send
              </Button>
            </ComposerPrimitive.Send>
          </div>
        </ComposerPrimitive.Root>
      </CardContent>
    </Card>
  );
};

const AssistantMessage: FC = () => {
  const content = useMessage((m) => m.content);
  const markdownText = React.useMemo(() => {
    if (!content) return "";
    if (typeof content === "string") return content;
    if (Array.isArray(content) && content.length > 0 && "text" in content[0]) {
      return content[0].text || "";
    }
    return "";
  }, [content]);

  return (
    <MessagePrimitive.Root className="grid grid-cols-[auto_auto_1fr] grid-rows-[auto_1fr] relative w-full max-w-[var(--thread-max-width)] py-4">
      <Card className="glass-assistant-message max-w-[calc(var(--thread-max-width)*0.8)] break-words leading-7 col-span-2 col-start-2 row-start-1 my-1.5">
        <CardContent className="p-4">
          <MemoryUI />
          <MarkdownRenderer
            markdownText={markdownText}
            showCopyButton={true}
            isDarkMode={true}
          />
        </CardContent>
      </Card>

      <AssistantActionBar />

      <BranchPicker className="col-start-2 row-start-2 -ml-2 mr-2" />
    </MessagePrimitive.Root>
  );
};

const AssistantActionBar: FC = () => {
  return (
    <ActionBarPrimitive.Root
      hideWhenRunning
      autohideFloat="single-branch"
      className="text-white flex gap-1 col-start-3 row-start-2 ml-1 data-[floating]:glass-card data-[floating]:absolute data-[floating]:rounded-md data-[floating]:p-1 data-[floating]:shadow-sm"
    >
      <ActionBarPrimitive.Copy asChild>
        <TooltipIconButton
          tooltip="Copy"
          className="hover:text-blue-300 glass-button"
        >
          <MessagePrimitive.If copied>
            <CheckIcon />
          </MessagePrimitive.If>
          <MessagePrimitive.If copied={false}>
            <CopyIcon />
          </MessagePrimitive.If>
        </TooltipIconButton>
      </ActionBarPrimitive.Copy>
      <ActionBarPrimitive.Reload asChild>
        <TooltipIconButton
          tooltip="Refresh"
          className="hover:text-blue-300 glass-button"
        >
          <RefreshCwIcon />
        </TooltipIconButton>
      </ActionBarPrimitive.Reload>
    </ActionBarPrimitive.Root>
  );
};

const BranchPicker: FC<BranchPickerPrimitive.Root.Props> = ({
  className,
  ...rest
}) => {
  return (
    <BranchPickerPrimitive.Root
      hideWhenSingleBranch
      className={cn(
        "text-white inline-flex items-center text-xs",
        className
      )}
      {...rest}
    >
      <BranchPickerPrimitive.Previous asChild>
        <TooltipIconButton
          tooltip="Previous"
          className="hover:text-blue-300 glass-button"
        >
          <ChevronLeftIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Previous>
      <span className="font-medium">
        <BranchPickerPrimitive.Number /> / <BranchPickerPrimitive.Count />
      </span>
      <BranchPickerPrimitive.Next asChild>
        <TooltipIconButton
          tooltip="Next"
          className="hover:text-blue-300 glass-button"
        >
          <ChevronRightIcon />
        </TooltipIconButton>
      </BranchPickerPrimitive.Next>
    </BranchPickerPrimitive.Root>
  );
};

const CircleStopIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 16 16"
      fill="currentColor"
      width="16"
      height="16"
    >
      <rect width="10" height="10" x="3" y="3" rx="2" />
    </svg>
  );
};

// Component for reuse in mobile drawer
const ThreadListItem: FC = () => {
  return (
    <ThreadListItemPrimitive.Root className="glass-thread-item focus-visible:ring-blue-500 flex items-center gap-2 rounded-lg transition-all focus-visible:outline-none focus-visible:ring-2">
      <ThreadListItemPrimitive.Trigger className="flex-grow px-3 py-2 text-start">
        <p className="text-sm text-white">
          <ThreadListItemPrimitive.Title fallback="New Chat" />
        </p>
      </ThreadListItemPrimitive.Trigger>
      <ThreadListItemPrimitive.Archive asChild>
        <TooltipIconButton
          className="hover:text-blue-300 text-white ml-auto mr-3 size-4 p-0"
          variant="ghost"
          tooltip="Archive thread"
        >
          <ArchiveIcon />
        </TooltipIconButton>
      </ThreadListItemPrimitive.Archive>
    </ThreadListItemPrimitive.Root>
  );
};