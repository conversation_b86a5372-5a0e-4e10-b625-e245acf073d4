"use client";

import { Calendar, Clock, ArrowRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui-enhanced/card";
import { But<PERSON> } from "@/components/ui-enhanced/button";

export function BlogSection() {
  const blogPosts = [
    {
      title: "The Future of AI Memory: How Persistent Context Changes Everything",
      excerpt: "Explore how memory-enabled AI agents are revolutionizing human-computer interaction by maintaining context across conversations and learning from user preferences.",
      date: "March 15, 2025",
      readTime: "5 min read",
      category: "AI Technology",
      gradient: "from-blue-500 to-indigo-600"
    },
    {
      title: "Building Trust with AI: Privacy and Security in Memory Agents",
      excerpt: "Learn about the advanced security measures and privacy protocols that protect your data while enabling powerful memory capabilities in AI systems.",
      date: "March 12, 2025",
      readTime: "7 min read",
      category: "Security",
      gradient: "from-cyan-500 to-blue-600"
    },
    {
      title: "Memory Agent Use Cases: Transforming Industries with AI",
      excerpt: "Discover real-world applications of memory agents across healthcare, education, customer service, and personal productivity sectors.",
      date: "March 10, 2025",
      readTime: "6 min read",
      category: "Use Cases",
      gradient: "from-indigo-500 to-purple-600"
    },
    {
      title: "Getting Started with Memory Agents: A Beginner's Guide",
      excerpt: "A comprehensive introduction to memory agents, their capabilities, and how to integrate them into your workflow for maximum productivity.",
      date: "March 8, 2025",
      readTime: "4 min read",
      category: "Getting Started",
      gradient: "from-blue-500 to-cyan-600"
    }
  ];

  const featuredPost = {
    title: "Understanding Memory Agents: The Complete Guide",
    excerpt: "Memory agents represent a breakthrough in AI technology, enabling artificial intelligence systems to maintain persistent context and learn from ongoing interactions. Unlike traditional chatbots that start fresh with each conversation, memory agents build cumulative understanding over time.",
    content: `Memory agents are AI systems designed to retain and utilize information from past interactions to provide more personalized and contextually relevant responses. They represent a significant evolution from stateless AI models to dynamic, learning systems that grow more useful over time.

**Key Characteristics:**
- **Persistent Memory**: Retains information across sessions
- **Contextual Understanding**: Connects current conversations to past interactions
- **Adaptive Learning**: Improves responses based on user feedback and preferences
- **Personalization**: Tailors responses to individual user needs and communication styles

**How Memory Agents Work:**
Memory agents use advanced vector databases and embedding techniques to store and retrieve relevant information. When you interact with a memory agent, it:

1. Processes your current input
2. Searches its memory for relevant past context
3. Combines current and historical information
4. Generates responses that reflect your preferences and history
5. Updates its memory with new information from the interaction

**Benefits for Users:**
- More natural, human-like conversations
- Reduced need to repeat information
- Improved task continuity across sessions
- Personalized recommendations and insights
- Enhanced productivity through contextual assistance

**Applications:**
Memory agents excel in scenarios requiring ongoing relationships, such as personal assistants, customer service, educational tutoring, and creative collaboration. They're particularly valuable for complex, multi-session projects where maintaining context is crucial.`,
    date: "March 18, 2025",
    readTime: "12 min read",
    category: "Deep Dive"
  };

  return (
    <section id="blog" className="py-20 px-6 relative z-10">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Latest <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">Insights</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Stay updated with the latest developments in AI memory technology and learn how to maximize your productivity with memory agents.
          </p>
        </div>

        {/* Featured Article */}
        <Card className="glass-card-prominent mb-16 overflow-hidden">
          <CardContent className="p-0">
            <div className="grid lg:grid-cols-2 gap-0">
              <div className="p-8 lg:p-12">
                <div className="flex items-center space-x-2 mb-4">
                  <span className="px-3 py-1 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border border-blue-500/30 rounded-full text-blue-300 text-sm font-medium">
                    {featuredPost.category}
                  </span>
                  <span className="text-gray-400 text-sm">Featured</span>
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                  {featuredPost.title}
                </h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  {featuredPost.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{featuredPost.date}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{featuredPost.readTime}</span>
                    </div>
                  </div>
                  <Button className="glass-button-primary">
                    Read More
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="relative h-64 lg:h-auto bg-gradient-to-br from-violet-900/20 to-purple-900/30 flex items-center justify-center">
                <div className="relative">
                  <div className="w-40 h-40 bg-gradient-to-br from-violet-500/30 to-purple-500/30 rounded-3xl flex items-center justify-center shadow-2xl">
                    <div className="w-24 h-24 bg-gradient-to-br from-violet-400 to-purple-400 rounded-2xl shadow-xl"></div>
                  </div>
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-violet-400/60 to-purple-400/60 rounded-xl"></div>
                  <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-violet-300/60 to-purple-300/60 rounded-lg"></div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Blog Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {blogPosts.map((post, index) => (
            <Card key={index} className="glass-card group hover:scale-105 transition-all duration-300 cursor-pointer overflow-hidden">
              <CardContent className="p-0">
                <div className="relative h-48 overflow-hidden flex items-center justify-center bg-gradient-to-br from-slate-800/30 to-slate-900/50">
                  {/* Create a visual pattern instead of broken images */}
                  <div className="relative w-full h-full flex items-center justify-center">
                    <div className={`w-24 h-24 bg-gradient-to-br ${post.gradient} rounded-2xl opacity-80 shadow-xl`}></div>
                    <div className={`absolute w-16 h-16 bg-gradient-to-br ${post.gradient} rounded-xl opacity-60 top-8 left-8`}></div>
                    <div className={`absolute w-12 h-12 bg-gradient-to-br ${post.gradient} rounded-lg opacity-40 bottom-8 right-8`}></div>
                  </div>
                  <div className="absolute top-4 left-4">
                    <span className={`px-3 py-1 bg-gradient-to-r ${post.gradient} rounded-full text-white text-xs font-medium shadow-lg`}>
                      {post.category}
                    </span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                </div>
                <div className="p-6">
                  <h4 className="font-semibold text-white mb-3 line-clamp-2 group-hover:text-blue-300 transition-colors">
                    {post.title}
                  </h4>
                  <p className="text-gray-300 text-sm mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{post.date}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Memory Agent Explanation */}
        <Card className="glass-card p-8 lg:p-12">
          <CardContent className="p-0">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-6 text-center">
                What Makes Memory Agents Special?
              </h3>
              <div className="prose prose-lg prose-invert max-w-none">
                <p className="text-gray-300 leading-relaxed mb-6">
                  {featuredPost.content.split('\n\n')[0]}
                </p>
                <div className="grid md:grid-cols-2 gap-8 mt-8">
                  <div>
                    <h4 className="text-lg font-semibold text-blue-300 mb-4">How They Work</h4>
                    <div className="space-y-3 text-gray-300">
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Process your current input</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Search memory for relevant context</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Combine current and historical data</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Generate personalized responses</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-cyan-300 mb-4">Key Benefits</h4>
                    <div className="space-y-3 text-gray-300">
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Natural, human-like conversations</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">No need to repeat information</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Improved task continuity</span>
                      </div>
                      <div className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-sm">Enhanced productivity</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <style jsx>{`
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .glass-card-prominent {
          background: rgba(255, 255, 255, 0.03);
          backdrop-filter: blur(30px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 16px 64px 0 rgba(31, 38, 135, 0.15);
        }
        .glass-button-primary {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.8), rgba(34, 211, 238, 0.8));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </section>
  );
}