"use client";

import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui-enhanced/card";

export function FeaturesSection() {
  const features = [
    {
      icon: Brain,
      title: "Persistent Memory",
      description: "Remembers your preferences, conversations, and context across all interactions for truly personalized experiences.",
      gradient: "from-blue-500 to-indigo-600"
    },
    {
      icon: Zap,
      title: "Lightning Fast",
      description: "Get instant responses powered by advanced AI while maintaining complete conversation history and context.",
      gradient: "from-cyan-500 to-blue-600"
    },
    {
      icon: Shield,
      title: "Privacy First",
      description: "Your data is encrypted and secure. We prioritize your privacy while delivering exceptional AI experiences.",
      gradient: "from-indigo-500 to-purple-600"
    },
    {
      icon: Clock,
      title: "Always Learning",
      description: "Continuously adapts to your communication style and preferences, becoming more helpful over time.",
      gradient: "from-blue-500 to-cyan-600"
    },
    {
      icon: Users,
      title: "Multi-Context",
      description: "Seamlessly handles different conversation contexts - work, personal, creative projects, and more.",
      gradient: "from-indigo-500 to-blue-600"
    },
    {
      icon: <PERSON><PERSON><PERSON>,
      title: "Smart Insights",
      description: "Provides intelligent suggestions and insights based on your conversation patterns and preferences.",
      gradient: "from-cyan-500 to-indigo-600"
    }
  ];

  return (
    <section id="features" className="py-20 px-6 relative z-10">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Powerful <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">Features</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience the next generation of AI conversations with advanced memory capabilities 
            that transform how you interact with artificial intelligence.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="glass-card feature-card group hover:scale-105 transition-all duration-300 cursor-pointer"
            >
              <CardContent className="p-6">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-200`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-blue-300 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed group-hover:text-gray-200 transition-colors">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Feature Highlight */}
        <div className="mt-20">
          <Card className="glass-card-prominent overflow-hidden">
            <CardContent className="p-0">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="p-12">
                  <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                    Memory that <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">evolves</span> with you
                  </h3>
                  <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                    Our advanced memory system doesn&#39;t just store information—it understands context, 
                    builds connections, and creates a comprehensive understanding of your preferences and needs.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span className="text-gray-200">Contextual understanding across conversations</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                      <span className="text-gray-200">Intelligent preference learning</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
                      <span className="text-gray-200">Long-term relationship building</span>
                    </div>
                  </div>
                </div>
                <div className="relative bg-gradient-to-br from-blue-950/50 to-indigo-950/50 p-12 flex items-center justify-center">
                  <div className="relative">
                    {/* Memory visualization */}
                    <div className="w-64 h-64 relative">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full animate-pulse"></div>
                      <div className="absolute inset-4 bg-gradient-to-br from-blue-500/30 to-cyan-500/30 rounded-full animate-pulse animation-delay-1000"></div>
                      <div className="absolute inset-8 bg-gradient-to-br from-blue-500/40 to-cyan-500/40 rounded-full animate-pulse animation-delay-2000"></div>
                      <div className="absolute inset-16 bg-gradient-to-br from-blue-500/60 to-cyan-500/60 rounded-full flex items-center justify-center">
                        <Brain className="w-16 h-16 text-white" />
                      </div>
                    </div>
                    {/* Floating memory nodes */}
                    <div className="absolute -top-4 -left-4 w-8 h-8 bg-blue-500 rounded-full opacity-60 animate-bounce"></div>
                    <div className="absolute -top-4 -right-4 w-6 h-6 bg-cyan-500 rounded-full opacity-60 animate-bounce animation-delay-500"></div>
                    <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-indigo-500 rounded-full opacity-60 animate-bounce animation-delay-1000"></div>
                    <div className="absolute -bottom-4 -right-4 w-8 h-8 bg-blue-400 rounded-full opacity-60 animate-bounce animation-delay-1500"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <style jsx>{`
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .glass-card-prominent {
          background: rgba(255, 255, 255, 0.03);
          backdrop-filter: blur(30px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 16px 64px 0 rgba(31, 38, 135, 0.15);
        }
        .feature-card:hover {
          box-shadow: 0 16px 64px 0 rgba(59, 130, 246, 0.2);
        }
        .animation-delay-500 {
          animation-delay: 0.5s;
        }
        .animation-delay-1000 {
          animation-delay: 1s;
        }
        .animation-delay-1500 {
          animation-delay: 1.5s;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
      `}</style>
    </section>
  );
}