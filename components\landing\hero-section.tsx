"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON>R<PERSON>, Spark<PERSON> } from "lucide-react";
import { Button } from "@/components/ui-enhanced/button";

export function HeroSection() {
  const [text, setText] = useState("");
  const fullText = "Your AI assistant that never forgets";
  
  useEffect(() => {
    let index = 0;
    const timer = setInterval(() => {
      setText(fullText.slice(0, index));
      index++;
      if (index > fullText.length) {
        clearInterval(timer);
      }
    }, 100);
    
    return () => clearInterval(timer);
  }, []);

  return (
    <section className="pt-32 pb-20 px-6 relative z-10">
      <div className="max-w-6xl mx-auto text-center">
        {/* Badge */}
        <div className="inline-flex items-center px-4 py-2 rounded-full glass-badge mb-8 hover:scale-105 transition-all duration-200">
          <Sparkles className="w-4 h-4 text-blue-400 mr-2" />
          <span className="text-sm text-blue-300 font-medium">Powered by Advanced Memory Technology</span>
        </div>

        {/* Main Headline */}
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400">
            {text}
          </span>
          <span className="animate-pulse">|</span>
        </h1>

        {/* Subheadline */}
        <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
          Experience conversations that build on themselves. Our memory agent learns your preferences, 
          remembers your context, and delivers truly personalized AI interactions.
        </p>

        {/* CTA Button - Centered and Bigger */}
        <div className="flex justify-center items-center mb-16">
          <Link href="/chat">
            <Button className="hero-cta-button px-12 py-6 text-xl font-bold hover:scale-105 transition-all duration-200 group shadow-2xl">
              Start Conversation
              <ArrowRight className="ml-3 w-6 h-6 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>

        {/* Hero Visual */}
        <div className="relative max-w-4xl mx-auto">
          <div className="glass-card p-8 rounded-3xl relative overflow-hidden">
            {/* Chat Interface Preview */}
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">U</span>
                </div>
                <div className="glass-message-user rounded-2xl px-4 py-3 max-w-xs">
                  <p className="text-white text-sm">I love hiking in mountain trails</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <div className="glass-message-ai rounded-2xl px-4 py-3 max-w-md">
                  <p className="text-white text-sm">I&#39;ll remember that you enjoy mountain hiking! Would you like some trail recommendations?</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">U</span>
                </div>
                <div className="glass-message-user rounded-2xl px-4 py-3 max-w-xs">
                  <p className="text-white text-sm">What outdoor activities should I try this weekend?</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <div className="glass-message-ai rounded-2xl px-4 py-3 max-w-md">
                  <p className="text-white text-sm">Since you love <span className="highlight-text">mountain hiking</span>, I&#39;d suggest exploring some local trails this weekend!</p>
                </div>
              </div>
            </div>

            {/* Floating memory indicators */}
            <div className="absolute top-4 right-4">
              <div className="flex items-center space-x-2 glass-memory-badge px-3 py-1 rounded-full">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-white">Memory Active</span>
              </div>
            </div>
          </div>

          {/* Floating elements */}
          <div className="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full opacity-60 animate-pulse"></div>
          <div className="absolute -bottom-6 -right-6 w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full opacity-40 animate-pulse animation-delay-1000"></div>
          <div className="absolute top-1/2 -right-8 w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full opacity-50 animate-pulse animation-delay-2000"></div>
        </div>
      </div>

      <style jsx>{`
        .glass-badge {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .hero-cta-button {
          background: linear-gradient(45deg, rgba(139, 92, 246, 0.9), rgba(124, 58, 237, 0.9));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
        }
        .hero-cta-button:hover {
          background: linear-gradient(45deg, rgba(139, 92, 246, 1), rgba(124, 58, 237, 1));
          box-shadow: 0 25px 50px rgba(139, 92, 246, 0.4);
        }
        .glass-message-user {
          background: rgba(59, 130, 246, 0.2);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-message-ai {
          background: rgba(79, 70, 229, 0.2);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-memory-badge {
          background: rgba(34, 211, 238, 0.2);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(34, 211, 238, 0.3);
        }
        .highlight-text {
          background: rgba(59, 130, 246, 0.3);
          padding: 2px 6px;
          border-radius: 4px;
          border: 1px solid rgba(59, 130, 246, 0.5);
        }
        .animation-delay-1000 {
          animation-delay: 1s;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
      `}</style>
    </section>
  );
}