"use client";

import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui-enhanced/button";
import { Card, CardContent } from "@/components/ui-enhanced/card";
import { Navigation } from "./navigation";
import { HeroSection } from "./hero-section";
import { FeaturesSection } from "./features-section";
import { BlogSection } from "./blog-section";
import Image from "next/image";

export function LandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950">
      {/* Animated GIF background - only in hero section */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0">
          <Image 
            src="/animation.gif" 
            alt="Background Animation"
            className="w-full h-screen object-cover opacity-30 mix-blend-screen"
            style={{ transform: 'var(--scroll-offset, translateY(0))' }}
            width={1920}
            height={1080}
          />
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-slate-950/50 to-slate-950"></div>
        </div>
      </div>

      <Navigation />
      <HeroSection />
      <FeaturesSection />

      {/* Background for remaining sections */}
      <div className="relative bg-gradient-to-b from-slate-950 via-indigo-950/50 to-slate-950">
        {/* Subtle animated elements for lower sections */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-pulse animation-delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/2 w-48 h-48 bg-cyan-500/10 rounded-full blur-3xl animate-pulse animation-delay-4000"></div>
        </div>

        <BlogSection />

        {/* CTA Section */}
        <section className="py-20 px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Card className="glass-card p-12 relative overflow-hidden">
              <CardContent className="p-0">
                <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                  Ready to experience the future of <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">AI memory</span>?
                </h2>
                <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                  Join thousands of users who are already experiencing the power of personalized AI conversations.
                </p>
                <Link href="/chat">
                  <Button className="glass-button-primary px-8 py-4 text-lg font-semibold hover:scale-105 transition-all duration-200">
                    Get Started Now
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-12 px-6 border-t border-white/10 relative z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-2xl font-bold text-white mb-4">C3ALabs</h3>
                <p className="text-gray-400">
                  Building the future of personalized AI experiences.
                </p>
              </div>
              <div>
                <h4 className="text-white font-semibold mb-4">Product</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#features" className="hover:text-blue-400 transition-colors">Features</a></li>
                  <li><a href="#" className="hover:text-blue-400 transition-colors">Pricing</a></li>
                  <li><a href="#" className="hover:text-blue-400 transition-colors">Documentation</a></li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-semibold mb-4">Company</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#" className="hover:text-blue-400 transition-colors">About</a></li>
                  <li><a href="#blog" className="hover:text-blue-400 transition-colors">Blog</a></li>
                  <li><a href="#" className="hover:text-blue-400 transition-colors">Contact</a></li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-semibold mb-4">Connect</h4>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#" className="hover:text-blue-400 transition-colors">Twitter</a></li>
                  <li><a href="#" className="hover:text-blue-400 transition-colors">GitHub</a></li>
                  <li><a href="#" className="hover:text-blue-400 transition-colors">LinkedIn</a></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-white/10 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; 2025 C3ALabs. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>

      <style jsx>{`
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .glass-button {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glass-button-primary {
          background: linear-gradient(45deg, rgba(59, 130, 246, 0.8), rgba(34, 211, 238, 0.8));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
    </div>
  );
}