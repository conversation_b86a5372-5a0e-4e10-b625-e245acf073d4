"use client";

import { useState } from "react";
import Link from "next/link";
import { Menu, X, LogOut } from "lucide-react";
import { Button } from "@/components/ui-enhanced/button";
import { useAuth } from "@/components/auth/auth-context";

export function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { logout } = useAuth();

  return (
    <nav className="fixed top-0 left-0 right-0 z-50">
      <div className="flex justify-center mt-4">
        <div className="glass-nav-container rounded-2xl w-auto inline-flex">
          <div className="px-6">
            <div className="flex items-center justify-between h-16 space-x-8">
              {/* Logo */}
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-lg">C</span>
                </div>
                <span className="text-xl font-bold text-white">C3ALabs</span>
              </Link>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-6">
                <a href="#features" className="text-gray-300 hover:text-white transition-colors font-medium">Features</a>
                <a href="#blog" className="text-gray-300 hover:text-white transition-colors font-medium">Blog</a>
                <a href="#" className="text-gray-300 hover:text-white transition-colors font-medium">Changelog</a>
              </div>

              {/* Desktop - Buttons only */}
              <div className="hidden md:flex items-center space-x-4 ml-16">
                <Link href="/chat">
                  <Button className="nav-cta-button px-6 py-2 hover:scale-105 transition-all duration-200 font-semibold">
                    Start Chat
                  </Button>
                </Link>
                <Button
                  onClick={logout}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:text-red-400 transition-colors"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden text-white hover:text-violet-400 transition-colors"
              >
                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden fixed top-20 left-4 right-4 glass-card p-4 rounded-lg z-50">
          <div className="flex flex-col space-y-4">
            <a href="#features" className="text-gray-300 hover:text-white transition-colors py-2">Features</a>
            <a href="#blog" className="text-gray-300 hover:text-white transition-colors py-2">Blog</a>
            <a href="#" className="text-gray-300 hover:text-white transition-colors py-2">Changelog</a>
            
            {/* Mobile buttons */}
            <div className="border-t border-white/10 pt-4 space-y-3">
              <Link href="/chat" className="block">
                <Button className="nav-cta-button w-full py-2">
                  Start Chat
                </Button>
              </Link>
              <Button
                onClick={logout}
                variant="ghost"
                className="w-full text-white hover:text-red-400 border border-red-400/30 bg-red-400/10"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        .glass-nav-container {
          background: rgba(30, 27, 75, 0.7);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(139, 92, 246, 0.3);
          box-shadow: 0 8px 32px 0 rgba(139, 92, 246, 0.1);
        }
        .glass-card {
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2);
        }
        .nav-cta-button {
          background: linear-gradient(45deg, rgba(139, 92, 246, 0.9), rgba(124, 58, 237, 0.9));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: white;
          border-radius: 12px;
        }
        .nav-cta-button:hover {
          background: linear-gradient(45deg, rgba(139, 92, 246, 1), rgba(124, 58, 237, 1));
          box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }
      `}</style>
    </nav>
  );
}